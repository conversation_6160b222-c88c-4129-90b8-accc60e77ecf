// gameEndScreen.js
import { renderDOM } from '../../msec/framework/dom.js';
import { sendAction } from '../multiplayer/sync.js';
import { navigate } from '../../msec/framework/router.js';
import { startNewSession } from '../multiplayer/socket.js';

// Guard to prevent multiple simultaneous calls
let isRendering = false;

export function gameEndScreen(root, winner, players) {
  if (isRendering) {
    console.log('[GameEndScreen] Already rendering, skipping duplicate call');
    return;
  }

  isRendering = true;
  console.log('Rendering GameEndScreen for winner:', winner);
  
  // Determine winner message
  let winnerMessage = '';
  let winnerColor = '#F6265A';
  
  if (winner === 'Draw') {
    winnerMessage = 'GAME ENDED IN A DRAW!';
    winnerColor = '#FFA500';
  } else {
    winnerMessage = `${winner.toUpperCase()} WINS!`;
  }

  // Create final scores
  const finalScores = players
    .sort((a, b) => {
      // Sort by alive status first, then by lives
      if (a.alive && !b.alive) return -1;
      if (!a.alive && b.alive) return 1;
      return b.lives - a.lives;
    })
    .map((player, index) => ({
      tag: 'div',
      attrs: { 
        style: `
          display: flex; 
          justify-content: space-between; 
          align-items: center;
          padding: 10px 20px; 
          margin: 5px 0; 
          background: ${player.alive ? 'rgba(0, 255, 0, 0.1)' : 'rgba(255, 0, 0, 0.1)'}; 
          border-radius: 8px;
          border-left: 4px solid ${player.alive ? '#00FF00' : '#FF0000'};
        `
      },
      children: [
        {
          tag: 'span',
          attrs: { style: 'font-weight: bold; color: #333;' },
          children: [`${index + 1}. ${player.nickname}`]
        },
        {
          tag: 'span',
          attrs: { style: 'color: #666;' },
          children: [player.alive ? `${player.lives} lives` : 'Eliminated']
        }
      ]
    }));

  const vdom = {
    tag: 'div',
    attrs: { 
      class: 'game-end-container',
      style: `
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 100vh;
        background: linear-gradient(135deg, #07131F 0%, #1a1a2e 100%);
        color: white;
        font-family: 'EngraversGothic', Arial, sans-serif;
        padding: 20px;
      `
    },
    children: [
      {
        tag: 'div',
        attrs: { 
          class: 'game-end-screen',
          style: `
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            max-width: 600px;
            width: 100%;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
          `
        },
        children: [
          {
            tag: 'h1',
            attrs: { 
              style: `
                color: ${winnerColor};
                font-size: 3rem;
                margin-bottom: 30px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
                letter-spacing: 4px;
              `
            },
            children: [winnerMessage]
          },
          {
            tag: 'div',
            attrs: { 
              class: 'final-scores',
              style: `
                background: rgba(255, 255, 255, 0.05);
                border-radius: 15px;
                padding: 20px;
                margin: 30px 0;
              `
            },
            children: [
              {
                tag: 'h3',
                attrs: { 
                  style: `
                    color: #FFF;
                    margin-bottom: 20px;
                    font-size: 1.5rem;
                    letter-spacing: 2px;
                  `
                },
                children: ['FINAL SCORES']
              },
              ...finalScores
            ]
          },
          {
            tag: 'div',
            attrs: { 
              class: 'game-end-actions',
              style: `
                display: flex;
                justify-content: center;
                margin-top: 30px;
              `
            },
            children: [
              {
                tag: 'button',
                attrs: {
                  id: 'restart-game-btn',
                  onclick: 'console.log("Inline onclick works!"); window.gameEndScreenRestart();',
                  style: `
                    background: linear-gradient(45deg, #4CAF50, #45a049);
                    color: white;
                    border: none;
                    padding: 15px 30px;
                    border-radius: 10px;
                    font-size: 1.1rem;
                    font-weight: bold;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    letter-spacing: 1px;
                    text-transform: uppercase;
                    font-family: 'EngraversGothic', Arial, sans-serif;
                    margin-right: 15px;
                  `
                },
                children: ['Play Again']
              },
              {
                tag: 'button',
                attrs: {
                  id: 'return-lobby-btn',
                  onclick: 'window.gameEndScreenLobby();',
                  style: `
                    background: linear-gradient(45deg, #F6265A, #FF4081);
                    color: white;
                    border: none;
                    padding: 15px 30px;
                    border-radius: 10px;
                    font-size: 1.1rem;
                    font-weight: bold;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    letter-spacing: 1px;
                    text-transform: uppercase;
                    font-family: 'EngraversGothic', Arial, sans-serif;
                  `
                },
                children: ['Return to Lobby']
              }
            ]
          }
        ]
      }
    ]
  };

  renderDOM(vdom, root);

  // Create global functions for inline onclick
  window.gameEndScreenRestart = () => {
    console.log('[GameEndScreen] *** GLOBAL RESTART FUNCTION CALLED ***');
    console.log('[GameEndScreen] sendAction function:', typeof sendAction);
    console.log('[GameEndScreen] About to call sendAction...');

    try {
      const result = sendAction({ type: 'restart' });
      console.log('[GameEndScreen] sendAction result:', result);
      console.log('[GameEndScreen] Restart action sent successfully via global function');

      // Provide better user feedback - show that we're returning to lobby
      const restartBtn = document.getElementById('restart-game-btn');
      if (restartBtn) {
        restartBtn.textContent = 'Returning to Lobby...';
        restartBtn.disabled = true;
        restartBtn.style.background = 'linear-gradient(45deg, #666, #555)';
        restartBtn.style.cursor = 'not-allowed';
      }

    } catch (error) {
      console.error('[GameEndScreen] Error in global restart function:', error);
      alert('Error in restart: ' + error.message);
    }
  };

  window.gameEndScreenLobby = () => {
    console.log('[GameEndScreen] Global lobby function called');
    try {
      sendAction({ type: 'lobby' });
      startNewSession();
      navigate('/');
      console.log('[GameEndScreen] Lobby action completed successfully');
    } catch (error) {
      console.error('[GameEndScreen] Error in global lobby function:', error);
    }
  };

  // Add event listeners with a small delay to ensure DOM is ready (as backup)
  setTimeout(() => {
    const restartBtn = document.getElementById('restart-game-btn');
    const lobbyBtn = document.getElementById('return-lobby-btn');

  // Add hover effects for restart button
  if (restartBtn) {

    restartBtn.addEventListener('mouseenter', () => {
      restartBtn.style.transform = 'translateY(-2px)';
      restartBtn.style.boxShadow = '0 8px 20px rgba(76, 175, 80, 0.4)';
    });

    restartBtn.addEventListener('mouseleave', () => {
      restartBtn.style.transform = 'translateY(0)';
      restartBtn.style.boxShadow = 'none';
    });
  }

  // Add hover effects for lobby button
  if (lobbyBtn) {
    lobbyBtn.addEventListener('mouseenter', () => {
      lobbyBtn.style.transform = 'translateY(-2px)';
      lobbyBtn.style.boxShadow = '0 8px 20px rgba(246, 38, 90, 0.4)';
    });

    lobbyBtn.addEventListener('mouseleave', () => {
      lobbyBtn.style.transform = 'translateY(0)';
      lobbyBtn.style.boxShadow = 'none';
    });
  }

  }, 100); // 100ms delay to ensure DOM is ready

  // Reset the rendering guard after a delay
  setTimeout(() => {
    isRendering = false;
  }, 200);
}
